import React from 'react';
import { useForm } from 'react-hook-form';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { teachersAPI } from '../../utils/api';
import { X } from 'lucide-react';
import toast from 'react-hot-toast';

export default function AddTeacherModal({ isOpen, onClose, onSuccess }) {
  const queryClient = useQueryClient();
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset
  } = useForm();

  const createTeacherMutation = useMutation({
    mutationFn: teachersAPI.create,
    onSuccess: () => {
      queryClient.invalidateQueries(['teachers']);
      toast.success('Teacher created successfully');
      reset();
      onSuccess?.();
    },
    onError: (error) => {
      toast.error(error.response?.data?.error || 'Failed to create teacher');
    },
  });

  const onSubmit = (data) => {
    createTeacherMutation.mutate(data);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        {/* Background overlay */}
        <div 
          className="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75"
          onClick={onClose}
        />

        {/* Modal panel */}
        <div className="inline-block w-full max-w-md p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-lg">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900">Add New Teacher</h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <X className="h-5 w-5" />
            </button>
          </div>

          <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
            <div>
              <label className="label">Full Name *</label>
              <input
                {...register('name', { required: 'Name is required' })}
                className="input"
                placeholder="Enter teacher's full name"
              />
              {errors.name && (
                <p className="mt-1 text-sm text-red-600">{errors.name.message}</p>
              )}
            </div>

            <div>
              <label className="label">Email *</label>
              <input
                {...register('email', {
                  required: 'Email is required',
                  pattern: {
                    value: /^\S+@\S+$/i,
                    message: 'Invalid email address',
                  },
                })}
                type="email"
                className="input"
                placeholder="<EMAIL>"
              />
              {errors.email && (
                <p className="mt-1 text-sm text-red-600">{errors.email.message}</p>
              )}
            </div>

            <div>
              <label className="label">Password *</label>
              <input
                {...register('password', {
                  required: 'Password is required',
                  minLength: {
                    value: 6,
                    message: 'Password must be at least 6 characters',
                  },
                })}
                type="password"
                className="input"
                placeholder="Enter password"
              />
              {errors.password && (
                <p className="mt-1 text-sm text-red-600">{errors.password.message}</p>
              )}
            </div>

            <div>
              <label className="label">Teacher ID *</label>
              <input
                {...register('teacher_id', { required: 'Teacher ID is required' })}
                className="input"
                placeholder="TCH001"
              />
              {errors.teacher_id && (
                <p className="mt-1 text-sm text-red-600">{errors.teacher_id.message}</p>
              )}
            </div>

            <div>
              <label className="label">Subject *</label>
              <select
                {...register('subject', { required: 'Subject is required' })}
                className="input"
              >
                <option value="">Select Subject</option>
                <option value="Mathematics">Mathematics</option>
                <option value="English">English</option>
                <option value="Physics">Physics</option>
                <option value="Chemistry">Chemistry</option>
                <option value="Biology">Biology</option>
                <option value="History">History</option>
                <option value="Geography">Geography</option>
                <option value="Computer Science">Computer Science</option>
              </select>
              {errors.subject && (
                <p className="mt-1 text-sm text-red-600">{errors.subject.message}</p>
              )}
            </div>

            <div>
              <label className="label">Phone</label>
              <input
                {...register('phone')}
                className="input"
                placeholder="Teacher's phone number"
              />
            </div>

            <div>
              <label className="label">Qualification</label>
              <input
                {...register('qualification')}
                className="input"
                placeholder="e.g., M.Sc Mathematics"
              />
            </div>

            <div>
              <label className="label">Experience (Years)</label>
              <input
                {...register('experience', {
                  min: { value: 0, message: 'Experience cannot be negative' }
                })}
                type="number"
                className="input"
                placeholder="Years of teaching experience"
              />
              {errors.experience && (
                <p className="mt-1 text-sm text-red-600">{errors.experience.message}</p>
              )}
            </div>

            <div className="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                onClick={onClose}
                className="btn-outline"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={createTeacherMutation.isLoading}
                className="btn-primary"
              >
                {createTeacherMutation.isLoading ? 'Creating...' : 'Create Teacher'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
