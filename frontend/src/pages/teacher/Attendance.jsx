import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useAuth } from '../../contexts/AuthContext';
import { classesAPI, attendanceAPI, studentsAPI } from '../../utils/api';
import { Calendar, Users, CheckCircle, XCircle, Clock, Save } from 'lucide-react';
import toast from 'react-hot-toast';

export default function TeacherAttendance() {
  const { user } = useAuth();
  const [selectedClass, setSelectedClass] = useState('');
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
  const [attendanceData, setAttendanceData] = useState({});
  const queryClient = useQueryClient();

  const { data: classesData } = useQuery({
    queryKey: ['teacher-classes', user?.teacher?.id],
    queryFn: () => classesAPI.getByTeacher(user?.teacher?.id),
    enabled: !!user?.teacher?.id,
  });

  const { data: studentsData } = useQuery({
    queryKey: ['class-students', selectedClass],
    queryFn: () => {
      const classInfo = classes.find(c => c.id.toString() === selectedClass);
      if (classInfo) {
        return studentsAPI.getAll({ grade: classInfo.grade, section: classInfo.section });
      }
      return { data: { students: [] } };
    },
    enabled: !!selectedClass,
  });

  const { data: existingAttendance } = useQuery({
    queryKey: ['class-attendance', selectedClass, selectedDate],
    queryFn: () => attendanceAPI.getByClass(selectedClass, { date: selectedDate }),
    enabled: !!selectedClass && !!selectedDate,
  });

  const markAttendanceMutation = useMutation({
    mutationFn: attendanceAPI.markAttendance,
    onSuccess: () => {
      queryClient.invalidateQueries(['class-attendance']);
      toast.success('Attendance marked successfully');
    },
    onError: (error) => {
      toast.error(error.response?.data?.error || 'Failed to mark attendance');
    },
  });

  const classes = classesData?.data?.classes || [];
  const students = studentsData?.data?.students || [];
  const existing = existingAttendance?.data?.attendance || [];

  // Initialize attendance data when students or existing attendance changes
  React.useEffect(() => {
    const initialData = {};
    students.forEach(student => {
      const existingRecord = existing.find(record => record.student_id === student.id);
      initialData[student.id] = existingRecord?.status || 'present';
    });
    setAttendanceData(initialData);
  }, [students, existing]);

  const handleAttendanceChange = (studentId, status) => {
    setAttendanceData(prev => ({
      ...prev,
      [studentId]: status
    }));
  };

  const handleSaveAttendance = async () => {
    if (!selectedClass || !selectedDate) {
      toast.error('Please select a class and date');
      return;
    }

    const attendanceRecords = Object.entries(attendanceData).map(([studentId, status]) => ({
      student_id: parseInt(studentId),
      class_id: parseInt(selectedClass),
      date: selectedDate,
      status,
      notes: ''
    }));

    try {
      for (const record of attendanceRecords) {
        await markAttendanceMutation.mutateAsync(record);
      }
    } catch (error) {
      // Error handling is done in the mutation
    }
  };

  const getStatusCounts = () => {
    const counts = { present: 0, absent: 0, late: 0 };
    Object.values(attendanceData).forEach(status => {
      counts[status] = (counts[status] || 0) + 1;
    });
    return counts;
  };

  const statusCounts = getStatusCounts();

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Attendance Management</h1>
        <p className="text-gray-600">Mark and manage student attendance</p>
      </div>

      {/* Controls */}
      <div className="card">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="label">Select Class</label>
            <select
              className="input"
              value={selectedClass}
              onChange={(e) => setSelectedClass(e.target.value)}
            >
              <option value="">Choose a class...</option>
              {classes.map((classItem) => (
                <option key={classItem.id} value={classItem.id}>
                  {classItem.name} - Grade {classItem.grade}{classItem.section}
                </option>
              ))}
            </select>
          </div>
          <div>
            <label className="label">Date</label>
            <input
              type="date"
              className="input"
              value={selectedDate}
              onChange={(e) => setSelectedDate(e.target.value)}
            />
          </div>
          <div className="flex items-end">
            <button
              onClick={handleSaveAttendance}
              disabled={!selectedClass || students.length === 0 || markAttendanceMutation.isLoading}
              className="btn-primary w-full"
            >
              <Save className="h-4 w-4 mr-2" />
              {markAttendanceMutation.isLoading ? 'Saving...' : 'Save Attendance'}
            </button>
          </div>
        </div>
      </div>

      {/* Summary */}
      {selectedClass && students.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="card">
            <div className="flex items-center">
              <Users className="h-8 w-8 text-blue-500 mr-3" />
              <div>
                <div className="text-2xl font-bold text-gray-900">{students.length}</div>
                <div className="text-sm text-gray-500">Total Students</div>
              </div>
            </div>
          </div>
          <div className="card">
            <div className="flex items-center">
              <CheckCircle className="h-8 w-8 text-green-500 mr-3" />
              <div>
                <div className="text-2xl font-bold text-gray-900">{statusCounts.present}</div>
                <div className="text-sm text-gray-500">Present</div>
              </div>
            </div>
          </div>
          <div className="card">
            <div className="flex items-center">
              <XCircle className="h-8 w-8 text-red-500 mr-3" />
              <div>
                <div className="text-2xl font-bold text-gray-900">{statusCounts.absent}</div>
                <div className="text-sm text-gray-500">Absent</div>
              </div>
            </div>
          </div>
          <div className="card">
            <div className="flex items-center">
              <Clock className="h-8 w-8 text-yellow-500 mr-3" />
              <div>
                <div className="text-2xl font-bold text-gray-900">{statusCounts.late}</div>
                <div className="text-sm text-gray-500">Late</div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Student List */}
      {selectedClass && students.length > 0 ? (
        <div className="card">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Student Attendance</h3>
          <div className="space-y-3">
            {students.map((student) => (
              <div key={student.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className="h-10 w-10 rounded-full bg-primary-100 flex items-center justify-center">
                    <span className="text-sm font-medium text-primary-600">
                      {student.name.charAt(0).toUpperCase()}
                    </span>
                  </div>
                  <div>
                    <div className="text-sm font-medium text-gray-900">{student.name}</div>
                    <div className="text-xs text-gray-500">ID: {student.student_id}</div>
                  </div>
                </div>
                <div className="flex space-x-2">
                  <button
                    onClick={() => handleAttendanceChange(student.id, 'present')}
                    className={`px-3 py-1 rounded-md text-sm font-medium ${
                      attendanceData[student.id] === 'present'
                        ? 'bg-green-100 text-green-800 border-green-200'
                        : 'bg-gray-100 text-gray-600 border-gray-200'
                    } border`}
                  >
                    <CheckCircle className="h-4 w-4 mr-1 inline" />
                    Present
                  </button>
                  <button
                    onClick={() => handleAttendanceChange(student.id, 'late')}
                    className={`px-3 py-1 rounded-md text-sm font-medium ${
                      attendanceData[student.id] === 'late'
                        ? 'bg-yellow-100 text-yellow-800 border-yellow-200'
                        : 'bg-gray-100 text-gray-600 border-gray-200'
                    } border`}
                  >
                    <Clock className="h-4 w-4 mr-1 inline" />
                    Late
                  </button>
                  <button
                    onClick={() => handleAttendanceChange(student.id, 'absent')}
                    className={`px-3 py-1 rounded-md text-sm font-medium ${
                      attendanceData[student.id] === 'absent'
                        ? 'bg-red-100 text-red-800 border-red-200'
                        : 'bg-gray-100 text-gray-600 border-gray-200'
                    } border`}
                  >
                    <XCircle className="h-4 w-4 mr-1 inline" />
                    Absent
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      ) : selectedClass ? (
        <div className="card">
          <div className="text-center py-8">
            <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <div className="text-gray-500">No students found for this class</div>
          </div>
        </div>
      ) : (
        <div className="card">
          <div className="text-center py-8">
            <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <div className="text-gray-500">Please select a class to mark attendance</div>
          </div>
        </div>
      )}
    </div>
  );
}
