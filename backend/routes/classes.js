const express = require('express');
const router = express.Router();
const classController = require('../controllers/classController');
const { authenticateToken, authorizeRoles } = require('../middleware/auth');
const { validate, schemas } = require('../middleware/validation');

// All routes require authentication
router.use(authenticateToken);

// Get all classes
router.get('/', classController.getAllClasses);

// Get specific class
router.get('/:id', classController.getClassById);

// Get classes by teacher
router.get('/teacher/:teacherId', classController.getClassesByTeacher);

// Get classes by student
router.get('/student/:studentId', classController.getClassesByStudent);

// Create new class (admin only)
router.post('/', authorizeRoles('admin'), validate(schemas.createClass), classController.createClass);

// Update class (admin only)
router.put('/:id', authorizeRoles('admin'), classController.updateClass);

// Delete class (admin only)
router.delete('/:id', authorizeRoles('admin'), classController.deleteClass);

module.exports = router;
