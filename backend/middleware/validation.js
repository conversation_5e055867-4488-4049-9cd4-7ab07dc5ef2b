const Joi = require('joi');

const validate = (schema) => {
  return (req, res, next) => {
    const { error } = schema.validate(req.body);
    if (error) {
      return res.status(400).json({
        error: 'Validation error',
        details: error.details.map(detail => detail.message)
      });
    }
    next();
  };
};

// Validation schemas
const schemas = {
  login: Joi.object({
    email: Joi.string().email().required(),
    password: Joi.string().min(6).required()
  }),

  createUser: Joi.object({
    email: Joi.string().email().required(),
    password: Joi.string().min(6).required(),
    role: Joi.string().valid('admin', 'teacher', 'student').required(),
    name: Joi.string().min(2).max(100).required(),
    phone: Joi.string().pattern(/^[0-9+\-\s()]+$/).optional()
  }),

  updateUser: Joi.object({
    email: Joi.string().email().optional(),
    name: Joi.string().min(2).max(100).optional(),
    phone: Joi.string().optional().allow('')
  }),

  createStudent: Joi.object({
    email: Joi.string().email().required(),
    password: Joi.string().min(6).required(),
    name: Joi.string().min(2).max(100).required(),
    phone: Joi.string().optional().allow('').allow(null),
    student_id: Joi.string().required(),
    grade: Joi.string().required(),
    section: Joi.string().required(),
    parent_name: Joi.string().min(2).max(100).optional().allow('').allow(null),
    parent_phone: Joi.string().optional().allow('').allow(null),
    address: Joi.string().max(500).optional().allow('').allow(null)
  }),

  updateStudent: Joi.object({
    grade: Joi.string().optional(),
    section: Joi.string().optional(),
    parent_name: Joi.string().min(2).max(100).optional().allow(''),
    parent_phone: Joi.string().optional().allow(''),
    address: Joi.string().max(500).optional()
  }),

  markAttendance: Joi.object({
    student_id: Joi.number().integer().positive().required(),
    class_id: Joi.number().integer().positive().required(),
    date: Joi.date().iso().required(),
    status: Joi.string().valid('present', 'absent', 'late').required(),
    notes: Joi.string().max(500).optional()
  }),

  createScore: Joi.object({
    student_id: Joi.number().integer().positive().required(),
    subject: Joi.string().required(),
    score: Joi.number().integer().min(0).required(),
    max_score: Joi.number().integer().min(1).required(),
    test_date: Joi.date().iso().required(),
    type: Joi.string().valid('quiz', 'exam', 'assignment', 'project').required(),
    description: Joi.string().max(500).optional()
  }),

  createTeacher: Joi.object({
    email: Joi.string().email().required(),
    password: Joi.string().min(6).required(),
    name: Joi.string().min(2).max(100).required(),
    phone: Joi.string().optional().allow('').allow(null),
    teacher_id: Joi.string().required(),
    subject: Joi.string().required(),
    qualification: Joi.string().optional().allow('').allow(null),
    experience: Joi.alternatives().try(
      Joi.number().integer().min(0),
      Joi.string().pattern(/^\d+$/).custom((value) => parseInt(value)),
      Joi.string().allow('').custom(() => 0)
    ).optional()
  }),

  updateTeacher: Joi.object({
    subject: Joi.string().optional(),
    qualification: Joi.string().optional(),
    experience: Joi.number().integer().min(0).optional(),
    phone: Joi.string().optional().allow('')
  }),

  createClass: Joi.object({
    name: Joi.string().required(),
    subject: Joi.string().required(),
    teacher_id: Joi.number().integer().positive().optional(),
    grade: Joi.string().required(),
    section: Joi.string().required(),
    schedule: Joi.string().optional(),
    virtual_link: Joi.string().uri().optional().allow('')
  }),

  updateClass: Joi.object({
    name: Joi.string().optional(),
    subject: Joi.string().optional(),
    teacher_id: Joi.number().integer().positive().optional(),
    grade: Joi.string().optional(),
    section: Joi.string().optional(),
    schedule: Joi.string().optional(),
    virtual_link: Joi.string().uri().optional().allow('')
  })
};

module.exports = {
  validate,
  schemas
};
