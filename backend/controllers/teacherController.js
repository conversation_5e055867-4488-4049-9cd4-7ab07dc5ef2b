const User = require('../models/User');
const Teacher = require('../models/Teacher');
const bcrypt = require('bcryptjs');

const teacherController = {
  // Get all teachers
  getAllTeachers: (req, res) => {
    try {
      const teachers = Teacher.getAll();
      res.json({
        success: true,
        data: { teachers }
      });
    } catch (error) {
      console.error('Error fetching teachers:', error);
      res.status(500).json({ error: 'Failed to fetch teachers' });
    }
  },

  // Get teacher stats
  getTeacherStats: (req, res) => {
    try {
      const teachers = Teacher.getAll();
      const stats = {
        total: teachers.length,
        bySubject: teachers.reduce((acc, teacher) => {
          acc[teacher.subject] = (acc[teacher.subject] || 0) + 1;
          return acc;
        }, {}),
        avgExperience: teachers.length > 0 ? 
          teachers.reduce((sum, t) => sum + (t.experience || 0), 0) / teachers.length : 0
      };
      
      res.json({
        success: true,
        data: { stats }
      });
    } catch (error) {
      console.error('Error fetching teacher stats:', error);
      res.status(500).json({ error: 'Failed to fetch teacher stats' });
    }
  },

  // Get teacher by ID
  getTeacherById: (req, res) => {
    try {
      const teacher = Teacher.findById(req.params.id);
      if (!teacher) {
        return res.status(404).json({ error: 'Teacher not found' });
      }
      
      res.json({
        success: true,
        data: { teacher }
      });
    } catch (error) {
      console.error('Error fetching teacher:', error);
      res.status(500).json({ error: 'Failed to fetch teacher' });
    }
  },

  // Create new teacher
  createTeacher: async (req, res) => {
    try {
      const { email, password, name, phone, teacher_id, subject, qualification, experience } = req.body;

      // Check if email already exists
      const existingUser = User.findByEmail(email);
      if (existingUser) {
        return res.status(400).json({ error: 'Email already exists' });
      }

      // Check if teacher_id already exists
      const existingTeacher = Teacher.findByTeacherId(teacher_id);
      if (existingTeacher) {
        return res.status(400).json({ error: 'Teacher ID already exists' });
      }

      // Hash password
      const hashedPassword = await bcrypt.hash(password, 10);

      // Create user first
      const user = User.create({
        email,
        password: hashedPassword,
        name,
        phone,
        role: 'teacher'
      });

      // Create teacher profile
      const teacher = Teacher.create({
        user_id: user.id,
        teacher_id,
        subject,
        qualification,
        experience: experience || 0
      });

      // Get complete teacher data
      const completeTeacher = Teacher.findById(teacher.id);

      res.status(201).json({
        message: 'Teacher created successfully',
        teacher: completeTeacher
      });
    } catch (error) {
      console.error('Error creating teacher:', error);
      res.status(500).json({ error: 'Failed to create teacher' });
    }
  },

  // Update teacher
  updateTeacher: (req, res) => {
    try {
      const teacher = Teacher.findById(req.params.id);
      if (!teacher) {
        return res.status(404).json({ error: 'Teacher not found' });
      }

      const updatedTeacher = Teacher.update(req.params.id, req.body);
      
      res.json({
        message: 'Teacher updated successfully',
        teacher: updatedTeacher
      });
    } catch (error) {
      console.error('Error updating teacher:', error);
      res.status(500).json({ error: 'Failed to update teacher' });
    }
  },

  // Delete teacher
  deleteTeacher: (req, res) => {
    try {
      const teacher = Teacher.findById(req.params.id);
      if (!teacher) {
        return res.status(404).json({ error: 'Teacher not found' });
      }

      // Delete teacher profile
      Teacher.delete(req.params.id);
      
      // Delete user account
      User.delete(teacher.user_id);

      res.json({ message: 'Teacher deleted successfully' });
    } catch (error) {
      console.error('Error deleting teacher:', error);
      res.status(500).json({ error: 'Failed to delete teacher' });
    }
  }
};

module.exports = teacherController;
