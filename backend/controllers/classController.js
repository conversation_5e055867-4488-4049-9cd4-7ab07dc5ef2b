const Class = require('../models/Class');
const Teacher = require('../models/Teacher');

const createClass = async (req, res) => {
  try {
    const { name, subject, teacher_id, grade, section, schedule, virtual_link } = req.body;

    const classData = Class.create({
      name,
      subject,
      teacher_id,
      grade,
      section,
      schedule,
      virtual_link
    });

    res.status(201).json({
      message: 'Class created successfully',
      class: classData
    });

  } catch (error) {
    console.error('Create class error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

const getAllClasses = async (req, res) => {
  try {
    const { grade, section, teacher_id } = req.query;
    const filters = {};
    
    if (grade) filters.grade = grade;
    if (section) filters.section = section;
    if (teacher_id) filters.teacher_id = teacher_id;

    const classes = Class.findAll(filters);
    res.json({ classes });

  } catch (error) {
    console.error('Get classes error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

const getClassById = async (req, res) => {
  try {
    const { id } = req.params;
    const classData = Class.findById(id);

    if (!classData) {
      return res.status(404).json({ error: 'Class not found' });
    }

    const studentCount = Class.getStudentCount(id);

    res.json({
      class: classData,
      studentCount
    });

  } catch (error) {
    console.error('Get class error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

const getClassesByTeacher = async (req, res) => {
  try {
    const { teacherId } = req.params;
    
    // Check if user can access this teacher's data
    if (req.user.role === 'teacher') {
      const teacher = Teacher.findByUserId(req.user.id);
      if (!teacher || teacher.id.toString() !== teacherId) {
        return res.status(403).json({ error: 'Access denied' });
      }
    }

    const classes = Class.findByTeacher(teacherId);
    res.json({ classes });

  } catch (error) {
    console.error('Get teacher classes error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

const getClassesByStudent = async (req, res) => {
  try {
    const { studentId } = req.params;
    
    // Check if user can access this student's data
    if (req.user.role === 'student') {
      const student = require('../models/Student').findByUserId(req.user.id);
      if (!student || student.id.toString() !== studentId) {
        return res.status(403).json({ error: 'Access denied' });
      }
    }

    const classes = Class.findByStudent(studentId);
    res.json({ classes });

  } catch (error) {
    console.error('Get student classes error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

const updateClass = async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    const classData = Class.findById(id);
    if (!classData) {
      return res.status(404).json({ error: 'Class not found' });
    }

    const updatedClass = Class.update(id, updateData);
    res.json({
      message: 'Class updated successfully',
      class: updatedClass
    });

  } catch (error) {
    console.error('Update class error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

const deleteClass = async (req, res) => {
  try {
    const { id } = req.params;

    const classData = Class.findById(id);
    if (!classData) {
      return res.status(404).json({ error: 'Class not found' });
    }

    const deleted = Class.delete(id);
    if (!deleted) {
      return res.status(400).json({ error: 'Failed to delete class' });
    }

    res.json({ message: 'Class deleted successfully' });

  } catch (error) {
    console.error('Delete class error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
};

module.exports = {
  createClass,
  getAllClasses,
  getClassById,
  getClassesByTeacher,
  getClassesByStudent,
  updateClass,
  deleteClass
};
